defmodule RivaAsh.Accounts.User do
  alias AshAuthentication.Strategy.Password.SignInPreparation

  use Ash.Resource,
    domain: RivaAsh.Accounts,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [
      AshAuthentication,
      AshAuthentication.Strategy.Password,
      AshArchival.Resource
    ]

  postgres do
    table("users")
    repo(RivaAsh.Repo)
  end

  # Configure soft delete functionality
  archive do
    attribute(:archived_at)
    base_filter?(false)
  end

  attributes do
    uuid_primary_key(:id)

    attribute(:email, :ci_string, allow_nil?: false, public?: true)
    attribute(:hashed_password, :string, allow_nil?: false, sensitive?: true)

    # Profile attributes
    attribute(:name, :string)
    attribute(:role, :atom, constraints: [one_of: [:superadmin, :admin, :user]], default: :user)

    create_timestamp(:inserted_at)
    update_timestamp(:updated_at)
  end

  identities do
    identity(:unique_email, [:email])
  end

  @signing_secret Application.compile_env(
                    :riva_ash,
                    :signing_secret,
                    "default_secret_change_me_in_prod"
                  )

  authentication do
    domain(RivaAsh.Accounts)
    session_identifier(:jti)

    strategies do
      password :password do
        identity_field(:email)
        hashed_password_field(:hashed_password)
        sign_in_tokens_enabled?(true)
        confirmation_required?(false)
        register_action_accept([:name, :email, :role])

        # The sign_in action is automatically defined by AshAuthentication
        # No need to manually define it here
      end
    end

    tokens do
      enabled?(true)
      token_resource(RivaAsh.Accounts.Token)
      signing_secret(@signing_secret)
      require_token_presence_for_authentication?(true)
    end
  end

  # Authorization policies
  policies do
    # Superadmins can do everything
    bypass actor_attribute_equals(:role, :superadmin) do
      authorize_if(always())
    end

    # Regular users can only read their own data via :me action
    policy action(:me) do
      authorize_if(actor_present())
    end

    # Only superadmins can view all users
    policy action(:all_users) do
      authorize_if(actor_attribute_equals(:role, :superadmin))
    end

    # Only superadmins can promote/demote users (role management actions)
    policy action(:promote_to_admin) do
      authorize_if(actor_attribute_equals(:role, :superadmin))
    end

    policy action(:promote_to_superadmin) do
      authorize_if(actor_attribute_equals(:role, :superadmin))
    end

    policy action(:demote_to_user) do
      authorize_if(actor_attribute_equals(:role, :superadmin))
    end

    # Users can update their own basic info (excluding role changes)
    policy action_type(:update) do
      authorize_if(expr(id == ^actor(:id)))
    end

    # Prevent unauthorized access to user data
    policy action_type(:read) do
      authorize_if(actor_attribute_equals(:role, :superadmin))
    end
  end

  # Define the actions for the User resource
  actions do
    defaults([:read, :update, :destroy])

    read :me do
      # Only returns the current user
      filter(expr(id == ^actor(:id)))
    end

    # Superadmin-only actions for system oversight
    read :all_users do
      description("Superadmin action to view all users for system oversight")
      # This action will be policy-protected to superadmin only
    end

    update :promote_to_admin do
      accept([:role])
      description("Promote a user to admin role - superadmin only")
      change(set_attribute(:role, :admin))
    end

    update :promote_to_superadmin do
      accept([:role])
      description("Promote a user to superadmin role - superadmin only")
      change(set_attribute(:role, :superadmin))
    end

    update :demote_to_user do
      accept([:role])
      description("Demote a user to regular user role - superadmin only")
      change(set_attribute(:role, :user))
    end
  end

  # Define the relationships
  relationships do
    # Add any relationships here
  end

  # Define the validations
  validations do
    validate(present([:email]))
  end

  # Define the calculations for the User resource
  calculations do
    calculate(:is_admin, :boolean, expr(role == :admin))
  end
end
